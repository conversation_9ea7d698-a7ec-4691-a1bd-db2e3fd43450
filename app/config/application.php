<?php
require('environment.php');

$app_dir = dirname(__DIR__);
$webroot_dir = $app_dir . '/webroot/blog';

/**
 * URLs
 */
define('WP_HOME', env('WP_HOME'));
define('WP_SITEURL', 'https://bon-voyage.ddev.site/blog/wp'); // env('WP_SITEURL')

/**
 * Custom Content Directory
 */
define('CONTENT_DIR', '/app');
define('WP_CONTENT_DIR', $webroot_dir . CONTENT_DIR);
define('WP_CONTENT_URL', WP_HOME . CONTENT_DIR);

/**
 * DB settings
 */
define('DB_NAME', env('RDS_NAME'));
define('DB_USER', env('RDS_USERNAME'));
define('DB_PASSWORD', env('RDS_PASSWORD'));
define('DB_HOST', env('RDS_HOSTNAME') ?: 'localhost');
define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');
$table_prefix = env('DB_PREFIX') ?: 'wp_';

/**
 * Authentication Unique Keys and Salts
 */
define('AUTH_KEY', env('WP_AUTH_KEY'));
define('SECURE_AUTH_KEY', env('WP_SECURE_AUTH_KEY'));
define('LOGGED_IN_KEY', env('WP_LOGGED_IN_KEY'));
define('NONCE_KEY', env('WP_NONCE_KEY'));
define('AUTH_SALT', env('WP_AUTH_SALT'));
define('SECURE_AUTH_SALT', env('WP_SECURE_AUTH_SALT'));
define('LOGGED_IN_SALT', env('WP_LOGGED_IN_SALT'));
define('NONCE_SALT', env('WP_NONCE_SALT'));

/**
 * Custom Settings
 */
define('AUTOMATIC_UPDATER_DISABLED', true);
define('DISABLE_WP_CRON', env('DISABLE_WP_CRON') ?: false);
define('DISALLOW_FILE_EDIT', true);

/**
 * Bootstrap WordPress
 */
if (!defined('ABSPATH')) {
  define('ABSPATH', $webroot_dir . '/wp/');
}
