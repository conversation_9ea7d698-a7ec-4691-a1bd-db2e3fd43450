<nav class="secondary-nav">
    <ul>
        <li>
            <?php
                echo $html->link('<b>USA</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg>', array(
                    'plugin'     => null,
                    'controller'          => 'destinations',
                    'action'              => 'view',
                    'section'             => 'destinations',
                    'destination_slug'    => 'usa_holidays',
                ), array(
                    'escape' => false,
                    'class' => 'secondary-nav__dest',
                    'data-dropdown' => 'usa-dropdown'
                ));
            ?>
        </li>
        <li>
            <?php
                echo $html->link('<b>Canada</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg>', array(
                    'plugin'     => null,
                    'controller'          => 'destinations',
                    'action'              => 'view',
                    'section'             => 'destinations',
                    'destination_slug'    => 'canada_holidays',
                ), array(
                    'escape' => false,
                    'class' => 'secondary-nav__dest',
                    'data-dropdown' => 'canada-dropdown'
                ));
            ?>
        </li>
        <li>
            <?php
                echo $html->link('<b>Far &amp; Wide</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg>', '#', array(
                    'escape' => false,
                    'class' => 'secondary-nav__dest',
                    'data-dropdown' => 'far-wide-dropdown'
                ));
            ?>
        </li>
        <li>
            <?php
                echo $html->link('<b>Holiday Types</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg>', array(
                    'plugin'     => null,
                    'controller' => 'holiday_types',
                    'action'     => 'index',
                    'section'    => 'holidays'
                ), array(
                    'escape' => false,
                    'class' => 'secondary-nav__dest',
                    'data-dropdown' => 'holiday-types-dropdown'
                ));
            ?>
        </li>
        <li>
            <?php
                echo $html->link('<b>What&rsquo;s hot</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg>', array(
                    'plugin'     => null,
                    'controller' => 'spotlights',
                    'action'     => 'index'
                ), array(
                    'escape' => false,
                    'class' => 'secondary-nav__dest',
                    'data-dropdown' => 'whats-hot-dropdown'
                ));
            ?>
        </li>

        <li>
            <?php
                echo $html->link('<b>About Us</b><svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" fill="none"><path fill="currentColor" fill-rule="evenodd" d="M7.24 14a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm.75-7L5.682 4.692l.702-.702L9.394 7l-3.01 3.01-.702-.702L7.99 7Z" clip-rule="evenodd"/></svg>', array(
                    'plugin'     => null,
                    'controller' => 'pages',
                    'action'     => 'view',
                    'page_slug'  => 'about_bon_voyage'
                ), array(
                    'escape' => false,
                    'class' => 'secondary-nav__dest',
                    'data-dropdown' => 'about-dropdown'
                ));
            ?>
        </li>
    </ul>
</nav>
