console.log('Navigation.js loading...');

document.addEventListener("DOMContentLoaded", function() {
    console.log('Initializing navigation...');

    try {
        const menu = new Mmenu("#mobile-menu", {
            "offCanvas": {
                "position": "left",
                "onClick": {
                    "close": false
                },
                "page": {
                    "selector": "body > .page-wrapper"
                }
            },
            "scrollBugFix": { "fix": true },
            "theme": "light",
            "slidingSubmenus": true,
            "extensions": [
                "pagedim-black",
            ],
            "navbar": {
                "title": "Menu",
                "titleLink": "parent"
            },
            "navbars": [
                {
                    content: [
                        '<a href="/" style="display: block; text-align: center;"><img src="/img/site/sprites/logos/bv-logo-red.svg" alt="Bon Voyage"></a>',

                    ],
                    position: "top"
                },
                {
                    "position": "top",
                    "content": [
                        "prev",
                        "title",
                    ]
                }
            ],
            "hooks": {
                "openPanel:before": function($panel) {
                    // Remove aria-hidden from all panels when opening
                    document.querySelectorAll('.mm-panel').forEach(panel => {
                        panel.removeAttribute('aria-hidden');
                    });
                },
                "closePanel:after": function($panel) {
                    // Use inert attribute instead of aria-hidden when closing
                    document.querySelectorAll('.mm-panel:not(.mm-panel--opened)').forEach(panel => {
                        panel.setAttribute('inert', '');
                    });
                    document.querySelector('.mm-panel--opened')?.removeAttribute('inert');
                },
                "close:after": function() {
                    // Reset hamburger/cross icons when menu is closed
                    const trigger = document.querySelector('.mmenu-trigger');
                    if (trigger) {
                        trigger.classList.remove('active');
                        const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                        const crossIcon = trigger.querySelector('.cross-icon');
                        if (hamburgerIcon && crossIcon) {
                            hamburgerIcon.style.display = 'block';
                            crossIcon.style.display = 'none';
                        }
                    }
                }
            }
        });

        const api = menu.API;

        // Add a binding for the close event
        api.bind('close:after', () => {
            // Reset hamburger/cross icons when menu is closed
            const trigger = document.querySelector('.mmenu-trigger');
            if (trigger) {
                trigger.classList.remove('active');
                const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                const crossIcon = trigger.querySelector('.cross-icon');
                if (hamburgerIcon && crossIcon) {
                    hamburgerIcon.style.display = 'block';
                    crossIcon.style.display = 'none';
                }
            }
        });

        api.bind("open:after", function() {
          console.log('locking scroll');
          document.documentElement.style.overflow = "hidden";
        });

        api.bind("close:after", function() {
          console.log('unlocking scroll');
          document.documentElement.style.overflow = "";
        });

        // Add click handler to menu trigger
        document.querySelector('.mmenu-trigger').addEventListener('click', (e) => {
            e.preventDefault();
            const trigger = e.currentTarget;

            // Check if menu is already open
            const isMenuOpen = document.body.classList.contains('mm-wrapper--opened');

            if (!isMenuOpen) {
                // Menu is closed, so open it and show cross icon
                trigger.classList.add('active');
                const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                const crossIcon = trigger.querySelector('.cross-icon');
                hamburgerIcon.style.display = 'none';
                crossIcon.style.display = 'block';
                api.open();
            } else {
                // Menu is open, so close it (icon will be changed by the close:after event)
                api.close();
            }
        });

        // Add click handler to close menu when clicking outside
        // document.addEventListener('click', (e) => {
        //     if (menu.node.menu.classList.contains('mm-menu--opened') &&
        //         !e.target.closest('#mobile-menu') &&
        //         !e.target.closest('.mmenu-trigger')) {
        //         api.close();
        //     }
        // });

        console.log('Mmenu initialized successfully', {
            menu: menu,
            api: api
        });

    } catch (error) {
        console.error('Mmenu initialization error:', error);
    }

    // Add media query listener
    const mediaQuery = window.matchMedia('(min-width: 1024px)');
    console.log('Media query initialized:', mediaQuery);

    function handleViewportChange(e) {
        console.log('Viewport change detected:', e);
        console.log('Matches desktop width:', e.matches);

        if (e.matches) {
            console.log('Attempting to close mmenu');
            const mmenuElement = document.querySelector('#mobile-menu');
            console.log('Mmenu element:', mmenuElement);

            if (mmenuElement) {
                // Try different methods to get the API
                const mmenuAPI = mmenuElement.mmApi ||
                                window.mmenu?.getInstance(mmenuElement) ||
                                mmenuElement.M_mmenu;

                console.log('Mmenu API:', mmenuAPI);

                if (mmenuAPI) {
                    try {
                        mmenuAPI.close();
                        console.log('Mmenu closed successfully');

                        // Reset hamburger/cross icons
                        const trigger = document.querySelector('.mmenu-trigger');
                        if (trigger) {
                            trigger.classList.remove('active');
                            const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                            const crossIcon = trigger.querySelector('.cross-icon');
                            if (hamburgerIcon && crossIcon) {
                                hamburgerIcon.style.display = 'block';
                                crossIcon.style.display = 'none';
                            }
                        }
                    } catch (error) {
                        console.error('Error closing mmenu:', error);

                        // Fallback: try to remove opened class
                        mmenuElement.classList.remove('mm-menu--opened');
                        document.body.classList.remove('mm-wrapper--opened');

                        // Reset hamburger/cross icons
                        const trigger = document.querySelector('.mmenu-trigger');
                        if (trigger) {
                            trigger.classList.remove('active');
                            const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                            const crossIcon = trigger.querySelector('.cross-icon');
                            if (hamburgerIcon && crossIcon) {
                                hamburgerIcon.style.display = 'block';
                                crossIcon.style.display = 'none';
                            }
                        }

                        console.log('Attempted class removal fallback');
                    }
                } else {
                    // If we can't find the API, try the class removal fallback
                    mmenuElement.classList.remove('mm-menu--opened');
                    document.body.classList.remove('mm-wrapper--opened');

                    // Reset hamburger/cross icons
                    const trigger = document.querySelector('.mmenu-trigger');
                    if (trigger) {
                        trigger.classList.remove('active');
                        const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                        const crossIcon = trigger.querySelector('.cross-icon');
                        if (hamburgerIcon && crossIcon) {
                            hamburgerIcon.style.display = 'block';
                            crossIcon.style.display = 'none';
                        }
                    }

                    console.log('No API found - used class removal fallback');
                }
            }
        }
    }

    // Add the listener
    try {
        mediaQuery.addEventListener('change', handleViewportChange);
    } catch (e1) {
        mediaQuery.addListener(handleViewportChange);
    }

    // Check initial state
    handleViewportChange(mediaQuery);
});
// Remove the global error handler for now

class MegaMenu {
    constructor() {
        console.log('MegaMenu: Initializing...');
        this.triggers = document.querySelectorAll('[data-dropdown]');
        this.dropdowns = document.querySelectorAll('.mega-menu__panel');
        this.sublistTriggers = document.querySelectorAll('.mega-menu__item:has(.mega-menu__sublist)');
        console.log('MegaMenu: Found triggers:', this.triggers.length);
        console.log('MegaMenu: Found dropdowns:', this.dropdowns.length);
        console.log('MegaMenu: Found sublist triggers:', this.sublistTriggers.length);
        this.activeDropdown = null;
        this.activeSublist = null;
        this.hoverDelay = 200;
        this.hoverTimeout = null;
        this.init();
        this.setupSublistHandlers();
    }

    setupSublistHandlers() {
        this.sublistTriggers.forEach(item => {
            const link = item.querySelector('.mega-menu__link');
            const sublist = item.querySelector('.mega-menu__sublist');

            if (link && sublist) {
                // Mouse enter on trigger
                item.addEventListener('mouseenter', () => {
                    this.openSublist(sublist, link);
                });

                // Mouse leave on trigger
                item.addEventListener('mouseleave', (e) => {
                    if (!e.relatedTarget || !e.relatedTarget.closest('.mega-menu__sublist')) {
                        this.closeSublist(sublist, link);
                    }
                });

                // Mouse enter on sublist
                sublist.addEventListener('mouseenter', () => {
                    clearTimeout(this.hoverTimeout);
                    link.style.setProperty('background-color', '#f5f5f5', 'important');
                    link.style.setProperty('color', '#a80000', 'important');
                });

                // Mouse leave on sublist
                sublist.addEventListener('mouseleave', () => {
                    this.closeSublist(sublist, link);
                });
            }
        });
    }

    openSublist(sublist, link) {
        const item = link.closest('.mega-menu__item');
        const dropdown = item.closest('.mega-menu__panel');
        const list = item.closest('ul');

        // Get the li's position relative to its parent ul
        const itemRect = item.getBoundingClientRect();
        const listRect = list.getBoundingClientRect();
        const relativeTop = itemRect.top - listRect.top;

        // Calculate top position using actual heights
        const sublistHeight = sublist.offsetHeight;
        const linkHeight = link.offsetHeight;
        const maxOffset = sublistHeight - linkHeight;
        const listHeight = list.offsetHeight;
        const percentage = relativeTop / listHeight;
        const topOffset = -maxOffset * percentage;

        // Position the sublist
        sublist.style.top = `${topOffset}px`;

        // Ensure consistent horizontal positioning
        sublist.style.left = '100%';
        sublist.style.position = 'absolute';
        sublist.style.transform = 'none';
        sublist.style.bottom = 'auto';

        // Set visibility and styling
        sublist.style.setProperty('opacity', '1', 'important');
        sublist.style.setProperty('visibility', 'visible', 'important');
        link.style.setProperty('background-color', '#f5f5f5', 'important');
        link.style.setProperty('color', '#a80000', 'important');
        this.activeSublist = sublist;
    }

    closeSublist(sublist, link) {
        sublist.style.setProperty('opacity', '0', 'important');
        sublist.style.setProperty('visibility', 'hidden', 'important');
        link.style.removeProperty('background-color');
        link.style.removeProperty('color');
        if (this.activeSublist === sublist) {
            this.activeSublist = null;
        }
    }

    init() {
        this.triggers.forEach(trigger => {
            const dropdownId = trigger.getAttribute('data-dropdown');
            const dropdown = document.getElementById(dropdownId);
            const parentListItem = trigger.closest('.secondary-nav li');
            console.log('MegaMenu: Setting up trigger for dropdown:', dropdownId);

            if (dropdown && parentListItem) {
                console.log('MegaMenu: Found matching dropdown for:', dropdownId);

                // Mouse enter on trigger
                trigger.addEventListener('mouseenter', () => {
                    console.log('MegaMenu: Mouse enter on trigger:', dropdownId);
                    clearTimeout(this.hoverTimeout);

                    // Close all other dropdowns before opening this one
                    this.dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown) {
                            const otherTriggerId = otherDropdown.id;
                            const otherTrigger = document.querySelector(`[data-dropdown="${otherTriggerId}"]`);
                            const otherListItem = otherTrigger?.closest('.secondary-nav li');
                            // Only close if we found both trigger and list item
                            if (otherTrigger && otherListItem) {
                                this.closeDropdown(otherDropdown, otherTrigger, otherListItem);
                            }
                        }
                    });

                    // Open the current dropdown
                    this.openDropdown(dropdown, trigger, parentListItem);
                });

                // Mouse leave on trigger
                trigger.addEventListener('mouseleave', (e) => {
                    // Check if mouse moved to the dropdown
                    if (!e.relatedTarget || !e.relatedTarget.closest('.mega-menu__panel')) {
                        this.startCloseTimer(dropdown, trigger, parentListItem);
                    }
                });

                // Mouse enter on dropdown
                dropdown.addEventListener('mouseenter', () => {
                    console.log('MegaMenu: Mouse enter on dropdown:', dropdownId);
                    clearTimeout(this.hoverTimeout);
                    parentListItem.classList.add('is-active');
                });

                // Mouse leave on dropdown
                dropdown.addEventListener('mouseleave', (e) => {
                    // Check if mouse moved to the trigger
                    if (!e.relatedTarget || !e.relatedTarget.closest('[data-dropdown]')) {
                        this.startCloseTimer(dropdown, trigger, parentListItem);
                    }
                });
            }
        });

        console.log('MegaMenu: Initialization complete');
    }

    startCloseTimer(dropdown, trigger, parentListItem) {
        this.hoverTimeout = setTimeout(() => {
            this.closeDropdown(dropdown, trigger, parentListItem);
        }, this.hoverDelay);
    }

    openDropdown(dropdown, trigger, parentListItem) {
        if (dropdown) {
            dropdown.style.setProperty('display', 'block', 'important');
        }
        if (parentListItem && parentListItem.classList) {
            parentListItem.classList.add('is-active');
        }
        this.activeDropdown = dropdown;
    }

    closeDropdown(dropdown, trigger, parentListItem) {
        if (dropdown) {
            dropdown.style.setProperty('display', 'none', 'important');
        }
        if (parentListItem && parentListItem.classList) {
            parentListItem.classList.remove('is-active');
        }
        if (this.activeDropdown === dropdown) {
            this.activeDropdown = null;
        }
    }

    closeAllDropdowns() {
        console.log('MegaMenu: Closing all dropdowns');
        this.dropdowns.forEach(dropdown => {
            const triggerId = dropdown.id;
            const trigger = document.querySelector(`[data-dropdown="${triggerId}"]`);
            const parentListItem = trigger?.closest('.secondary-nav li');
            // Only close if we found both trigger and list item
            if (trigger && parentListItem) {
                this.closeDropdown(dropdown, trigger, parentListItem);
            }
        });
    }
}

// Initialize when DOM is ready and all scripts are loaded
window.addEventListener('load', () => {
    console.log('Window loaded, initializing MegaMenu');
    const megaMenu = new MegaMenu();
});
