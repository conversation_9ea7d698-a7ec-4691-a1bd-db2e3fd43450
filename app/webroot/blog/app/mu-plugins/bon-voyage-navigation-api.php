<?php
/**
 * Plugin Name: Bon Voyage Navigation API Integration
 * Description: Integrates the navigation from the main site into the WordPress blog using API endpoints
 * Version: 1.0
 * Author: Bon Voyage
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class BonVoyage_Navigation_API {
    /**
     * Constructor
     */
    public function __construct() {
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // DISABLED: Add mobile menu HTML to the footer (will be moved to the header via JS)
        // This is now handled by bon-voyage-navigation.php to avoid duplicate mobile menus
        // add_action('wp_footer', array($this, 'add_mobile_menu_html'));

        // Add megamenu HTML to the footer
        add_action('wp_footer', array($this, 'add_megamenu_html'));

        // Add script to move the megamenu to the right place
        add_action('wp_footer', array($this, 'add_megamenu_placement_script'));

        // Modify the header template
        add_filter('timber/context', array($this, 'modify_header_template'));
    }

    /**
     * Add script to move the megamenu to the right place
     */
    public function add_megamenu_placement_script() {
        ?>
        <script>
            jQuery(document).ready(function($) {
                // Check for multiple megamenu elements
                if ($('.mega-menu').length > 1) {
                    // Only log in debug mode
                    if (window.bonVoyageNav && window.bonVoyageNav.isDebug) {
                        console.log('[Megamenu] Multiple mega-menu elements found (' + $('.mega-menu').length + '). Using the first one.');
                    }

                    // Keep only the first megamenu element and remove others
                    $('.mega-menu').not(':first').remove();

                    // Make sure the first megamenu has the placeholder ID
                    if (!$('.mega-menu:first').attr('id')) {
                        $('.mega-menu:first').attr('id', 'megamenu-placeholder');
                    }
                } else if (window.bonVoyageNav && window.bonVoyageNav.isDebug) {
                    console.log('[Megamenu] Single mega-menu element found, no cleanup needed');
                }
            });
        </script>
        <?php
    }

    /**
     * Enqueue necessary scripts and styles
     */
    public function enqueue_scripts() {
        // Enqueue the shared navigation CSS from the main site
        // Use the absolute URL to ensure it's loaded correctly
        // We need to bypass WordPress URL handling to get to the parent directory
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'];
        $main_site_url = $protocol . $host;

        // Register the style with an absolute URL
        wp_register_style(
            'navigation-css',
            $main_site_url . '/css/navigation.css',
            array(),
            '1.0'
        );

        // Manually modify the registered style's src to ensure it doesn't get the WordPress base URL prepended
        global $wp_styles;
        if (isset($wp_styles->registered['navigation-css'])) {
            $wp_styles->registered['navigation-css']->src = $main_site_url . '/css/navigation.css';
        }

        // Enqueue the style
        wp_enqueue_style('navigation-css');

        // Add debug script to check if CSS is loaded
        if (isset($_GET['debug_css'])) {
            add_action('wp_footer', function() use ($main_site_url) {
                echo "
                <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Check if navigation.css is loaded
                        var isLoaded = false;
                        var styleSheets = document.styleSheets;
                        for (var i = 0; i < styleSheets.length; i++) {
                            try {
                                if (styleSheets[i].href && styleSheets[i].href.includes('/css/navigation.css')) {
                                    console.log('FOUND navigation.css: ' + styleSheets[i].href);
                                    isLoaded = true;
                                    break;
                                }
                            } catch (e) {
                                // Cross-origin stylesheet, skip it
                            }
                        }
                        console.log('navigation.css loaded: ' + isLoaded);
                        console.log('URL used: " . $main_site_url . "/css/navigation.css');
                    });
                </script>
                ";
            });
        }

        // Enqueue jQuery
        wp_enqueue_script('jquery');

        // Enqueue mmenu JavaScript from CDN
        wp_enqueue_script(
            'mmenu-core-js',
            'https://cdnjs.cloudflare.com/ajax/libs/mmenu-js/9.3.0/mmenu.min.js',
            array('jquery'),
            '8.5.24',
            true
        );

        // Enqueue mmenu CSS from CDN
        wp_enqueue_style(
            'mmenu-core-css',
            'https://cdnjs.cloudflare.com/ajax/libs/mmenu-js/9.3.0/mmenu.min.css',
            array(),
            '8.5.24'
        );

        // Add configuration script with debug flag
        $debug_flag = isset($_GET['debug_menu']) ? 'true' : 'false';
        wp_add_inline_script('jquery', 'window.bonVoyageNav = { isDebug: ' . $debug_flag . ' };', 'before');

        // We no longer load the main site's navigation.js script
        // This is to avoid duplicate script loading and conflicts

        // Add custom CSS for the WordPress blog
        $custom_css = "
        /* WordPress blog specific navigation styles */

        /* Mobile menu styles - white background with grey text and lines */
        .mm-menu {
            --mm-color-background: #fff !important;
            --mm-color-text: #4b4742 !important;
            --mm-color-button: #4b4742 !important;
            --mm-color-border: rgba(75,71,66,0.2) !important;
            background-color: #fff !important;
        }



        /* When menu is active, show cross and hide hamburger */
        .mmenu-trigger.active .hamburger-icon {
            display: none !important;
        }

        .mmenu-trigger.active .cross-icon {
            display: block !important;
        }

        /* Mobile menu styles */

        /* Header layout and positioning */
        .page-header {
            position: relative;
            z-index: 1000;
            background: #a80000;
            box-shadow: 0 7px 2px rgba(0, 0, 0, 0.3);
        }

        .page-header__inner {
            display: flex;
            align-items: center;
            /* padding: 0; */
            width: 100%;
            max-width: 1280px;
            margin: 0 auto;
            position: relative;
        }

        /* Logo styling */
        .page-header__logo {
            display: block;
            margin: 20px 18px 20px 0;
            flex-shrink: 0;
        }

        .page-header__logo img {
            width: 220px;
            height: 50px;
            display: block;
        }

        /* Desktop menu container */
        .desktop-menu {
            display: flex;
            align-self: stretch;
            align-items: flex-start;
            margin-left: 15px;
            margin-right: 40px;
            margin-top: 15px;
            flex-grow: 1;
            flex-shrink: 0;
            width: max-content;
            position: static;
        }
        .desktop-menu__inner {
            display: flex;
            flex-direction: column;
        }

        /* Primary navigation */
        .primary-nav {
            padding: 0 0 7px 5px;
            display: flex;
        }

        .primary-nav ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .primary-nav li {
            padding: 0 10px;
            margin: 0;
        }

        /* Secondary navigation */
        .secondary-nav {
            display: flex;
            margin: 0;
            /* margin-top: 10px; */
        }

        .secondary-nav ul {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .secondary-nav li {
            margin: 0;
            position: relative;
            padding: 0 10px;
        }

        .secondary-nav__dest {
            color: #fff;
            display: block;
            padding: 10px 5px;
            text-decoration: none;
            white-space: nowrap;
        }

        /* Search toggle button */
        .search-toggle {
            position: absolute;
            top: 23px;
            right: 19px;
            width: 25px;
            height: 25px;
            border: none;
            cursor: pointer;
            padding: 0;
            /* background: transparent; */
        }

        .search-toggle img {
            width: 25px;
            height: 25px;
            display: block;
        }

        /* Search box */
        .primary-search--page-header {
            /* position: absolute;
            top: 12px;
            right: 18px;
            width: 200px; */
        }

        .primary-search__input {
            position: relative;
        }

        .primary-search__input input[type=text] {
            border-radius: 2px;
            border: 0;
            height: 35px;
            width: 100%;
            padding: 12px;
        }

        .primary-search__input .button-wrap {
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            display: flex;
            width: 40px;
            align-items: center;
            justify-content: center;
        }

        .primary-search__input .button-wrap button {
            /* background: transparent; */
            border: none;
            margin: 0;
            padding: 0;
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .primary-search__input .button-wrap button img {
            width: 18px;
            height: 18px;
            display: block;
        }

        /* Mobile menu trigger button */
        .mmenu-trigger {
            position: absolute;
            top: 12px;
            left: 8px;
            border: none;
            background: none;
            padding: 0;
            cursor: pointer;
            z-index: 1001;
            padding: 10px;
        }

        /* Hide cross icon by default */
        .cross-icon {
            display: none !important;
            transition: display 0.2s ease;
        }

        .hamburger-icon {
            display: block !important;
            transition: display 0.2s ease;
        }

        /* Show cross icon when menu is active */
        .mmenu-trigger.active .hamburger-icon {
            display: none !important;
        }

        .mmenu-trigger.active .cross-icon {
            display: block !important;
        }

        /* Ensure cross icon is visible when menu is active */
        html.mm-wrapper_opened .mmenu-trigger .cross-icon {
            display: block !important;
        }

        html.mm-wrapper_opened .mmenu-trigger .hamburger-icon {
            display: none !important;
        }

        /* Fix for WordPress admin bar */
        .admin-bar .mm-menu {
            top: 32px;
        }

        @media screen and (max-width: 782px) {
            .admin-bar .mm-menu {
                top: 46px;
            }
        }

        /* Mega Menu Styles */
        .mega-menu {
            position: absolute;
            left: 0;
            right: 0;
            top: auto;
            bottom: 0;
            z-index: 998;
            width: 100%;
        }

        /* Position the megamenu placeholder */
        /* #megamenu-placeholder {
            position: relative;
            z-index: 997;
            width: 100%;
        }*/

        /* Screen reader only text */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }
        ";

        wp_add_inline_style('mmenu-core-css', $custom_css);
    }

    /**
     * Modify the header template
     */
    public function modify_header_template($context) {
        // Add a flag to indicate that we're using the new navigation
        $context['use_new_navigation'] = true;

        // Add a direct link to the navigation CSS as a fallback
        // add_action('wp_head', function() {
        //     $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        //     $host = $_SERVER['HTTP_HOST'];
        //     $main_site_url = $protocol . $host;

        //     // echo '<link rel="stylesheet" href="' . $main_site_url . '/css/navigation.css" type="text/css" media="all">';
        // }, 999); // High priority to ensure it loads after other styles

        return $context;
    }

    /**
     * Add mobile menu HTML to the footer
     * This fetches the mmenu HTML from the main site API
     * TEMPORARILY DISABLED to prevent conflicts with multiple mmenu initializations
     */
    public function add_mobile_menu_html() {
        // TEMPORARILY DISABLED to prevent conflicts with multiple mmenu initializations
        return;

        // For now, use the fallback HTML directly
        $mmenu_html = $this->get_fallback_mmenu_html();

        // Output the mmenu HTML
        echo $mmenu_html;

        // Add script to initialize mmenu
        ?>
        <script>
            jQuery(document).ready(function($) {
                // Create a debug flag
                const isDebug = window.bonVoyageNav && window.bonVoyageNav.isDebug;

                // Helper function for logging
                function log(message, data) {
                    if (isDebug) {
                        console.log('[MMenu] ' + message, data || '');
                    }
                }

                function error(message, data) {
                    console.error('[MMenu] ' + message, data || '');
                }

                // Move the mobile menu to the placeholder
                if ($('#mobile-menu-placeholder').length) {
                    log('Moving mobile menu to placeholder');
                    $('#mobile-menu-placeholder').replaceWith($('#mobile-menu'));
                }

                // Check if the mmenu trigger button exists
                if ($('.mmenu-trigger').length > 0) {
                    log('Initializing mmenu');
                    try {
                        // TEMPORARILY DISABLED: Mmenu initialization to prevent conflicts
                        log('Mmenu initialization in bon-voyage-navigation-api.php is temporarily disabled');

                        // Dummy API to prevent errors
                        var api = {
                            bind: function() {},
                            close: function() {}
                        };

                        /* Original code commented out
                        var menu = new Mmenu("#mobile-menu", {
                            "offCanvas": {
                                "position": "left",
                                "onClick": {
                                    "close": false
                                }
                            },
                            "scrollBugFix": { "fix": true },
                            "theme": "light",
                            "slidingSubmenus": true,
                            "extensions": [
                                "pagedim-black",
                            ],
                            "navbar": {
                                "title": "Menu",
                                "titleLink": "parent"
                            },
                            "navbars": [
                                {
                                    "content": [
                                        '<img src="/img/site/sprites/logos/bv-logo-red.svg" alt="Bon Voyage" style="height: 50px;">',
                                    ],
                                    "position": "top"
                                },
                                {
                                    "position": "top",
                                    "content": [
                                        "prev",
                                        "title",
                                    ]
                                }
                            ],
                            "hooks": {
                                "open:after": function() {
                                    log('Locking scroll for mobile menu');
                                    document.documentElement.style.overflow = "hidden";
                                },
                                "close:after": function() {
                                    log('Unlocking scroll after mobile menu close');
                                    document.documentElement.style.overflow = "";
                                }
                            }
                        });

                        var api = menu.API;
                        */

                                    // Reset hamburger/cross icons when menu is closed
                                    const trigger = document.querySelector('.mmenu-trigger');
                                    if (trigger) {
                                        trigger.classList.remove('active');
                                        const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                                        const crossIcon = trigger.querySelector('.cross-icon');
                                        if (hamburgerIcon && crossIcon) {
                                            hamburgerIcon.style.display = 'block';
                                            crossIcon.style.display = 'none';
                                        }
                                    }
                                }
                            }
                        });

                        var api = menu.API;
                        log('Mmenu initialized successfully');

                        // Add click handler to menu trigger
                        document.querySelector('.mmenu-trigger').addEventListener('click', (e) => {
                            e.preventDefault();
                            const trigger = e.currentTarget;

                            // Check if menu is already open
                            const isMenuOpen = document.body.classList.contains('mm-wrapper_opened');

                            if (!isMenuOpen) {
                                // Menu is closed, so open it and show cross icon
                                trigger.classList.add('active');
                                const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                                const crossIcon = trigger.querySelector('.cross-icon');
                                hamburgerIcon.style.display = 'none';
                                crossIcon.style.display = 'block';
                                api.open();
                            } else {
                                // Menu is open, so close it (icon will be changed by the close:after event)
                                api.close();
                            }
                        });

                        // Add media query listener
                        const mediaQuery = window.matchMedia('(min-width: 1024px)');
                        log('Media query initialized');

                        function handleViewportChange(e) {
                            log('Viewport change detected', { matches: e.matches });

                            if (e.matches) {
                                // Desktop view - close mobile menu if open
                                const mmenuElement = document.querySelector('#mobile-menu');

                                if (mmenuElement) {
                                    // Try different methods to get the API
                                    const mmenuAPI = mmenuElement.mmApi ||
                                                    window.mmenu?.getInstance(mmenuElement) ||
                                                    mmenuElement.M_mmenu;

                                    if (mmenuAPI) {
                                        try {
                                            mmenuAPI.close();
                                            log('Mobile menu closed for desktop view');
                                        } catch (error) {
                                            error('Error closing mobile menu:', error);

                                            // Fallback: try to remove opened class
                                            mmenuElement.classList.remove('mm-menu--opened');
                                            document.body.classList.remove('mm-wrapper--opened');

                                            // Reset hamburger/cross icons
                                            const trigger = document.querySelector('.mmenu-trigger');
                                            if (trigger) {
                                                trigger.classList.remove('active');
                                                const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                                                const crossIcon = trigger.querySelector('.cross-icon');
                                                if (hamburgerIcon && crossIcon) {
                                                    hamburgerIcon.style.display = 'block';
                                                    crossIcon.style.display = 'none';
                                                }
                                            }
                                        }
                                    } else {
                                        // If we can't find the API, try the class removal fallback
                                        mmenuElement.classList.remove('mm-menu--opened');
                                        document.body.classList.remove('mm-wrapper--opened');

                                        // Reset hamburger/cross icons
                                        const trigger = document.querySelector('.mmenu-trigger');
                                        if (trigger) {
                                            trigger.classList.remove('active');
                                            const hamburgerIcon = trigger.querySelector('.hamburger-icon');
                                            const crossIcon = trigger.querySelector('.cross-icon');
                                            if (hamburgerIcon && crossIcon) {
                                                hamburgerIcon.style.display = 'block';
                                                crossIcon.style.display = 'none';
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // Add the listener
                        try {
                            if (typeof mediaQuery.addEventListener === 'function') {
                                mediaQuery.addEventListener('change', handleViewportChange);
                            } else if (typeof mediaQuery.addListener === 'function') {
                                mediaQuery.addListener(handleViewportChange);
                            }
                        } catch (err) {
                            error('Could not add media query listener:', err);
                        }

                        // Check initial state
                        handleViewportChange(mediaQuery);

                    } catch (error) {
                        error('Mmenu initialization error:', error);
                    }
                }

                // Initialize megamenu dropdowns
                if (isDebug) {
                    log('Setting up megamenu dropdown handlers');
                    log('Number of dropdown triggers: ' + $('.secondary-nav__dest[data-dropdown]').length);
                }

                // Add hover event handlers for the megamenu
                $('.secondary-nav__dest[data-dropdown]').on('mouseenter', function() {
                    var dropdownId = $(this).data('dropdown');
                    var dropdown = $('#' + dropdownId);

                    // Hide all other dropdowns
                    $('.mega-menu__panel').removeClass('is-active').css('display', 'none');

                    // Show this dropdown
                    dropdown.addClass('is-active').css('display', 'block');

                    // Add active class to parent li
                    $(this).closest('li').addClass('is-active');
                });

                // Add hover event handlers for the megamenu panels
                $('.mega-menu__panel').on('mouseenter', function() {
                    $(this).css('display', 'block');
                }).on('mouseleave', function() {
                    $(this).css('display', 'none');
                });

                // Handle mouse leave for secondary nav
                $('.secondary-nav').on('mouseleave', function() {
                    $('.mega-menu__panel').css('display', 'none');
                    $('.secondary-nav li').removeClass('is-active');
                });

                // Handle mouse enter for dropdowns
                $('.mega-menu__panel').on('mouseenter', function() {
                    // Find the corresponding trigger
                    var dropdownId = $(this).attr('id');
                    var trigger = $('[data-dropdown="' + dropdownId + '"]');

                    // Add active class to parent li
                    trigger.closest('li').addClass('is-active');
                });

                // Handle mouse leave for dropdowns
                $('.mega-menu__panel').on('mouseleave', function(e) {
                    // Check if mouse moved to the trigger
                    if (!e.relatedTarget || !$(e.relatedTarget).closest('[data-dropdown]').length) {
                        $(this).css('display', 'none');
                        $(this).removeClass('is-active');
                        $('.secondary-nav li').removeClass('is-active');
                    }
                });
            });
        </script>
        <?php
    }

    /**
     * Add megamenu HTML to the footer
     * This fetches the megamenu HTML from the main site API
     */
    public function add_megamenu_html() {
        // Check if we should add the megamenu HTML
        // We'll add it only if the debug flag is set, to avoid duplicate megamenus
        if (isset($_GET['debug_menu'])) {
            // For now, use the fallback HTML directly
            $megamenu_html = $this->get_fallback_megamenu_html();

            // Output the megamenu HTML
            echo $megamenu_html;
        }
    }

    /**
     * Fetch the mmenu HTML from the main site API
     */
    private function fetch_mmenu_html() {
        // Get the main site URL
        $main_site_url = $this->get_main_site_url();

        // Fetch the mmenu HTML from the API
        $response = wp_remote_get($main_site_url . '/api/navigation/mmenu');

        // Check if the request was successful
        if (is_wp_error($response)) {
            // If there was an error, return a fallback HTML
            return $this->get_fallback_mmenu_html();
        }

        // Get the response body
        $body = wp_remote_retrieve_body($response);

        // Check if the body is empty
        if (empty($body)) {
            // If the body is empty, return a fallback HTML
            return $this->get_fallback_mmenu_html();
        }

        // Return the mmenu HTML
        return $body;
    }

    /**
     * Fetch the megamenu HTML from the main site API
     */
    private function fetch_megamenu_html() {
        // Get the main site URL
        $main_site_url = $this->get_main_site_url();

        // Fetch the megamenu HTML from the API
        $response = wp_remote_get($main_site_url . '/api/navigation/megamenu');

        // Check if the request was successful
        if (is_wp_error($response)) {
            // If there was an error, return a fallback HTML
            return $this->get_fallback_megamenu_html();
        }

        // Get the response body
        $body = wp_remote_retrieve_body($response);

        // Check if the body is empty
        if (empty($body)) {
            // If the body is empty, return a fallback HTML
            return $this->get_fallback_megamenu_html();
        }

        // Return the megamenu HTML
        return $body;
    }

    /**
     * Get the main site URL
     */
    private function get_main_site_url() {
        // Get the current site URL
        $current_site_url = get_site_url();

        // Parse the URL
        $parsed_url = parse_url($current_site_url);

        // Get the host
        $host = $parsed_url['host'];

        // Get the scheme
        $scheme = $parsed_url['scheme'];

        // Check if the host is a subdomain of bonvoyage.co.uk
        if (strpos($host, 'blog.') === 0) {
            // If it's a subdomain, replace 'blog.' with nothing
            $main_host = str_replace('blog.', '', $host);
        } else {
            // Otherwise, use the same host
            $main_host = $host;
        }

        // Return the main site URL
        return $scheme . '://' . $main_host;
    }

    /**
     * Get a fallback mmenu HTML
     * This is used if the API request fails
     */
    private function get_fallback_mmenu_html() {
        // Return a comprehensive mmenu HTML
        return '<nav id="mobile-menu" role="navigation" aria-label="Mobile navigation">
            <ul class="mm-listview">
                <li class="mm-listitem has_children">
                    <a href="/destinations/usa_holidays" class="mm-listitem__text">USA</a>
                    <ul class="mm-listview">
                        <li class="mm-listitem"><a href="/destinations/new_york" class="mm-listitem__text">New York</a></li>
                        <li class="mm-listitem"><a href="/destinations/florida" class="mm-listitem__text">Florida</a></li>
                        <li class="mm-listitem"><a href="/destinations/california" class="mm-listitem__text">California</a></li>
                        <li class="mm-listitem"><a href="/destinations/las_vegas" class="mm-listitem__text">Las Vegas</a></li>
                        <li class="mm-listitem"><a href="/destinations/hawaii" class="mm-listitem__text">Hawaii</a></li>
                        <li class="mm-listitem"><a href="/destinations/washington_dc" class="mm-listitem__text">Washington DC</a></li>
                        <li class="mm-listitem"><a href="/destinations/chicago" class="mm-listitem__text">Chicago</a></li>
                        <li class="mm-listitem"><a href="/destinations/boston" class="mm-listitem__text">Boston</a></li>
                        <li class="mm-listitem"><a href="/destinations/new_england" class="mm-listitem__text">New England</a></li>
                        <li class="mm-listitem"><a href="/destinations/the_deep_south" class="mm-listitem__text">The Deep South</a></li>
                        <li class="mm-listitem"><a href="/destinations/texas" class="mm-listitem__text">Texas</a></li>
                        <li class="mm-listitem"><a href="/destinations/alaska" class="mm-listitem__text">Alaska</a></li>
                    </ul>
                </li>
                <li class="mm-listitem has_children">
                    <a href="/destinations/canada_holidays" class="mm-listitem__text">Canada</a>
                    <ul class="mm-listview">
                        <li class="mm-listitem"><a href="/destinations/toronto" class="mm-listitem__text">Toronto</a></li>
                        <li class="mm-listitem"><a href="/destinations/vancouver" class="mm-listitem__text">Vancouver</a></li>
                        <li class="mm-listitem"><a href="/destinations/montreal" class="mm-listitem__text">Montreal</a></li>
                        <li class="mm-listitem"><a href="/destinations/canadian_rockies" class="mm-listitem__text">Canadian Rockies</a></li>
                        <li class="mm-listitem"><a href="/destinations/niagara_falls" class="mm-listitem__text">Niagara Falls</a></li>
                        <li class="mm-listitem"><a href="/destinations/banff" class="mm-listitem__text">Banff</a></li>
                        <li class="mm-listitem"><a href="/destinations/quebec" class="mm-listitem__text">Quebec</a></li>
                        <li class="mm-listitem"><a href="/destinations/calgary" class="mm-listitem__text">Calgary</a></li>
                        <li class="mm-listitem"><a href="/destinations/ottawa" class="mm-listitem__text">Ottawa</a></li>
                    </ul>
                </li>
                <li class="mm-listitem has_children">
                    <a href="/holidays" class="mm-listitem__text">Holiday Types</a>
                    <ul class="mm-listview">
                        <li class="mm-listitem"><a href="/holidays/multi_centre_combination_holidays" class="mm-listitem__text">Multi-Centre Holidays</a></li>
                        <li class="mm-listitem"><a href="/holidays/fly_drive_holidays" class="mm-listitem__text">Fly-Drive Holidays</a></li>
                        <li class="mm-listitem"><a href="/holidays/route_66_holidays" class="mm-listitem__text">Route 66 Holidays</a></li>
                        <li class="mm-listitem"><a href="/holidays/cruise_holidays" class="mm-listitem__text">Cruise Holidays</a></li>
                        <li class="mm-listitem"><a href="/holidays/rail_holidays" class="mm-listitem__text">Rail Holidays</a></li>
                        <li class="mm-listitem"><a href="/holidays/ranch_holidays" class="mm-listitem__text">Ranch Holidays</a></li>
                        <li class="mm-listitem"><a href="/holidays/motorhome_holidays" class="mm-listitem__text">Motorhome Holidays</a></li>
                        <li class="mm-listitem"><a href="/holidays/escorted_tours" class="mm-listitem__text">Escorted Tours</a></li>
                        <li class="mm-listitem"><a href="/holidays/winter_holidays" class="mm-listitem__text">Winter Holidays</a></li>
                        <li class="mm-listitem"><a href="/holidays/family_holidays" class="mm-listitem__text">Family Holidays</a></li>
                        <li class="mm-listitem"><a href="/holidays/luxury_holidays" class="mm-listitem__text">Luxury Holidays</a></li>
                    </ul>
                </li>
                <li class="mm-listitem has_children">
                    <a href="/spotlights" class="mm-listitem__text">What\'s Hot</a>
                    <ul class="mm-listview">
                        <li class="mm-listitem"><a href="/spotlights/special_offers" class="mm-listitem__text">Special Offers</a></li>
                        <li class="mm-listitem"><a href="/spotlights/new_destinations" class="mm-listitem__text">New Destinations</a></li>
                        <li class="mm-listitem"><a href="/spotlights/seasonal_highlights" class="mm-listitem__text">Seasonal Highlights</a></li>
                        <li class="mm-listitem"><a href="/spotlights/featured_holidays" class="mm-listitem__text">Featured Holidays</a></li>
                    </ul>
                </li>
                <li class="mm-listitem has_children">
                    <a href="/page/holiday_information" class="mm-listitem__text">Holiday Info</a>
                    <ul class="mm-listview">
                        <li class="mm-listitem"><a href="/page/visa_information" class="mm-listitem__text">Visa Information</a></li>
                        <li class="mm-listitem"><a href="/page/travel_insurance" class="mm-listitem__text">Travel Insurance</a></li>
                        <li class="mm-listitem"><a href="/page/health_advice" class="mm-listitem__text">Health Advice</a></li>
                        <li class="mm-listitem"><a href="/page/passport_information" class="mm-listitem__text">Passport Information</a></li>
                        <li class="mm-listitem"><a href="/page/travel_money" class="mm-listitem__text">Travel Money</a></li>
                    </ul>
                </li>
                <li class="mm-listitem has_children">
                    <a href="/page/about_bon_voyage" class="mm-listitem__text">About Us</a>
                    <ul class="mm-listview">
                        <li class="mm-listitem"><a href="/page/our_customers_say" class="mm-listitem__text">Our Customers Say</a></li>
                        <li class="mm-listitem"><a href="/page/fully_bonded_for_your_protection" class="mm-listitem__text">Fully Bonded for Your Protection</a></li>
                        <li class="mm-listitem"><a href="/page/telephone_numbers" class="mm-listitem__text">Telephone Numbers</a></li>
                        <li class="mm-listitem"><a href="/page/address_and_registered_details" class="mm-listitem__text">Address and Registered Details</a></li>
                        <li class="mm-listitem"><a href="/page/bon_voyage_feefo_rating" class="mm-listitem__text">Bon Voyage Feefo Rating</a></li>
                        <li class="mm-listitem"><a href="/page/finding_us" class="mm-listitem__text">Finding Us</a></li>
                        <li class="mm-listitem"><a href="/page/careers" class="mm-listitem__text">Careers</a></li>
                        <li class="mm-listitem"><a href="/page/press_centre" class="mm-listitem__text">Press Centre</a></li>
                    </ul>
                </li>
                <li class="mm-listitem"><a href="/blog" class="mm-listitem__text">Blog</a></li>
                <li class="mm-listitem"><a href="/faqs" class="mm-listitem__text">FAQs</a></li>
                <li class="mm-listitem"><a href="/make_an_enquiry" class="mm-listitem__text">Make an Enquiry</a></li>
            </ul>
        </nav>';
    }

    /**
     * Get a fallback megamenu HTML
     * This is used if the API request fails
     */
    private function get_fallback_megamenu_html() {
        // Return a comprehensive megamenu HTML
        return '<div class="mega-menu">
            <!-- USA Dropdown -->
            <div class="mega-menu__panel" id="usa-dropdown">
                <div class="mega-menu__inner">
                    <div class="mega-menu__section">
                        <h3>Popular Destinations</h3>
                        <ul class="mega-menu__list mega-menu__list--columns">
                            <li class="mega-menu__item">
                                <a href="/destinations/new_york" class="mega-menu__link" title="New York">New York</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/florida" class="mega-menu__link" title="Florida">Florida</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/california" class="mega-menu__link" title="California">California</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/las_vegas" class="mega-menu__link" title="Las Vegas">Las Vegas</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/hawaii" class="mega-menu__link" title="Hawaii">Hawaii</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/washington_dc" class="mega-menu__link" title="Washington DC">Washington DC</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/chicago" class="mega-menu__link" title="Chicago">Chicago</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/boston" class="mega-menu__link" title="Boston">Boston</a>
                            </li>
                        </ul>
                    </div>
                    <div class="mega-menu__section">
                        <h3>Regions</h3>
                        <ul class="mega-menu__list">
                            <li class="mega-menu__item">
                                <a href="/destinations/new_england" class="mega-menu__link" title="New England">New England</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/the_deep_south" class="mega-menu__link" title="The Deep South">The Deep South</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/texas" class="mega-menu__link" title="Texas">Texas</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/alaska" class="mega-menu__link" title="Alaska">Alaska</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/rocky_mountains" class="mega-menu__link" title="Rocky Mountains">Rocky Mountains</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/pacific_northwest" class="mega-menu__link" title="Pacific Northwest">Pacific Northwest</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Canada Dropdown -->
            <div class="mega-menu__panel" id="canada-dropdown">
                <div class="mega-menu__inner">
                    <div class="mega-menu__section">
                        <h3>Popular Destinations</h3>
                        <ul class="mega-menu__list mega-menu__list--columns">
                            <li class="mega-menu__item">
                                <a href="/destinations/toronto" class="mega-menu__link" title="Toronto">Toronto</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/vancouver" class="mega-menu__link" title="Vancouver">Vancouver</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/montreal" class="mega-menu__link" title="Montreal">Montreal</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/canadian_rockies" class="mega-menu__link" title="Canadian Rockies">Canadian Rockies</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/niagara_falls" class="mega-menu__link" title="Niagara Falls">Niagara Falls</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/banff" class="mega-menu__link" title="Banff">Banff</a>
                            </li>
                        </ul>
                    </div>
                    <div class="mega-menu__section">
                        <h3>Regions</h3>
                        <ul class="mega-menu__list">
                            <li class="mega-menu__item">
                                <a href="/destinations/quebec" class="mega-menu__link" title="Quebec">Quebec</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/calgary" class="mega-menu__link" title="Calgary">Calgary</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/ottawa" class="mega-menu__link" title="Ottawa">Ottawa</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/british_columbia" class="mega-menu__link" title="British Columbia">British Columbia</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/alberta" class="mega-menu__link" title="Alberta">Alberta</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Far & Wide Dropdown -->
            <div class="mega-menu__panel" id="far-wide-dropdown">
                <div class="mega-menu__inner">
                    <div class="mega-menu__section">
                        <h3>Far & Wide</h3>
                        <ul class="mega-menu__list">
                            <li class="mega-menu__item">
                                <a href="/landing_pages/south_america" class="mega-menu__link" title="South America">
                                    South America
                                </a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/destinations/worldwide_holidays" class="mega-menu__link" title="Worldwide">
                                    Worldwide
                                </a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/landing_pages/cruises" class="mega-menu__link" title="Cruises">
                                    Cruises
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Holiday Types Dropdown -->
            <div class="mega-menu__panel" id="holiday-types-dropdown">
                <div class="mega-menu__inner">
                    <div class="mega-menu__section">
                        <h3>Holiday Types</h3>
                        <ul class="mega-menu__list mega-menu__list--columns">
                            <li class="mega-menu__item">
                                <a href="/holidays/multi_centre_combination_holidays" class="mega-menu__link" title="Multi-Centre Holidays">Multi-Centre Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/fly_drive_holidays" class="mega-menu__link" title="Fly-Drive Holidays">Fly-Drive Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/route_66_holidays" class="mega-menu__link" title="Route 66 Holidays">Route 66 Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/cruise_holidays" class="mega-menu__link" title="Cruise Holidays">Cruise Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/rail_holidays" class="mega-menu__link" title="Rail Holidays">Rail Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/ranch_holidays" class="mega-menu__link" title="Ranch Holidays">Ranch Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/motorhome_holidays" class="mega-menu__link" title="Motorhome Holidays">Motorhome Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/escorted_tours" class="mega-menu__link" title="Escorted Tours">Escorted Tours</a>
                            </li>
                        </ul>
                    </div>
                    <div class="mega-menu__section">
                        <h3>Special Interest</h3>
                        <ul class="mega-menu__list">
                            <li class="mega-menu__item">
                                <a href="/holidays/winter_holidays" class="mega-menu__link" title="Winter Holidays">Winter Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/family_holidays" class="mega-menu__link" title="Family Holidays">Family Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/luxury_holidays" class="mega-menu__link" title="Luxury Holidays">Luxury Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/city_breaks" class="mega-menu__link" title="City Breaks">City Breaks</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/beach_holidays" class="mega-menu__link" title="Beach Holidays">Beach Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/holidays/adventure_holidays" class="mega-menu__link" title="Adventure Holidays">Adventure Holidays</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- What\'s Hot Dropdown -->
            <div class="mega-menu__panel" id="whats-hot-dropdown">
                <div class="mega-menu__inner">
                    <div class="mega-menu__section">
                        <h3>What\'s Hot</h3>
                        <ul class="mega-menu__list mega-menu__list--columns">
                            <li class="mega-menu__item">
                                <a href="/spotlights/special_offers" class="mega-menu__link" title="Special Offers">Special Offers</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/spotlights/new_destinations" class="mega-menu__link" title="New Destinations">New Destinations</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/spotlights/seasonal_highlights" class="mega-menu__link" title="Seasonal Highlights">Seasonal Highlights</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/spotlights/featured_holidays" class="mega-menu__link" title="Featured Holidays">Featured Holidays</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/spotlights/early_booking_offers" class="mega-menu__link" title="Early Booking Offers">Early Booking Offers</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- About Us Dropdown -->
            <div class="mega-menu__panel" id="about-dropdown">
                <div class="mega-menu__inner">
                    <div class="mega-menu__section">
                        <h3>About Bon Voyage</h3>
                        <ul class="mega-menu__list mega-menu__list--columns">
                            <li class="mega-menu__item">
                                <a href="/page/our_customers_say" class="mega-menu__link" title="Our Customers Say">Our Customers Say</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/page/fully_bonded_for_your_protection" class="mega-menu__link" title="Fully Bonded for Your Protection">Fully Bonded for Your Protection</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/page/telephone_numbers" class="mega-menu__link" title="Telephone Numbers">Telephone Numbers</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/page/address_and_registered_details" class="mega-menu__link" title="Address and Registered Details">Address and Registered Details</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/page/bon_voyage_feefo_rating" class="mega-menu__link" title="Bon Voyage Feefo Rating">Bon Voyage Feefo Rating</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/page/finding_us" class="mega-menu__link" title="Finding Us">Finding Us</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/page/careers" class="mega-menu__link" title="Careers">Careers</a>
                            </li>
                            <li class="mega-menu__item">
                                <a href="/page/press_centre" class="mega-menu__link" title="Press Centre">Press Centre</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>';
    }
}

// Initialize the plugin
new BonVoyage_Navigation_API();
